#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix bibliography format by removing numbered entries
and making all entries use consistent unnumbered format.
"""

import re

def fix_bibliography_format(filename):
    """Fix bibliography format by removing numbered entries."""
    
    # Read the file
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match numbered bibitem entries
    # \bibitem[123]{key} -> \bibitem{key}
    pattern = r'\\bibitem\[[0-9]+\]\{([^}]+)\}'
    replacement = r'\\bibitem{\1}'
    
    # Replace all numbered entries with unnumbered ones
    fixed_content = re.sub(pattern, replacement, content)
    
    # Count the changes made
    original_matches = len(re.findall(pattern, content))
    
    # Write the fixed content back
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"Fixed {original_matches} numbered bibliography entries")
    print("All bibliography entries now use consistent unnumbered format")
    return original_matches

if __name__ == "__main__":
    filename = "Scoping_Review_Solar_Technology_Maintenance_Challenges.tex"
    changes = fix_bibliography_format(filename)
    print(f"\nBibliography format fixed! {changes} entries converted.")
    print("Now compile with: pdflatex document.tex (run twice)")
