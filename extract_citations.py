import re

def extract_cited_keys_and_clean_bibliography():
    filename = "Scoping_Review_Solar_Technology_Maintenance_Challenges.tex"

    # Read the file
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()

    # Extract all citation keys from \cite{} and \citep{} commands
    citation_pattern = r'\\cite[pt]?\{([^}]+)\}'
    citation_matches = re.findall(citation_pattern, content)

    # Split multiple citations and clean up
    cited_keys = set()
    for match in citation_matches:
        # Split by comma for multiple citations like \citep{key1,key2,key3}
        keys = [key.strip() for key in match.split(',')]
        cited_keys.update(keys)

    print(f"Found {len(cited_keys)} unique citation keys in the text:")
    for key in sorted(cited_keys):
        print(f"  - {key}")

    # Extract all bibliography entries
    bibitem_pattern = r'\\bibitem\{([^}]+)\}'
    bibitem_matches = re.findall(bibitem_pattern, content)

    print(f"\nFound {len(bibitem_matches)} bibliography entries:")

    # Find uncited entries
    uncited_keys = set(bibitem_matches) - cited_keys
    print(f"\nUncited entries ({len(uncited_keys)}):")
    for key in sorted(uncited_keys):
        print(f"  - {key}")

    return cited_keys, uncited_keys, content

def remove_uncited_bibliography_entries(content, uncited_keys):
    """Remove uncited bibliography entries from the content."""

    # Split content into lines for easier processing
    lines = content.split('\n')
    cleaned_lines = []

    i = 0
    while i < len(lines):
        line = lines[i]

        # Check if this line starts a bibliography entry
        bibitem_match = re.match(r'\\bibitem\{([^}]+)\}', line)
        if bibitem_match:
            key = bibitem_match.group(1)
            if key in uncited_keys:
                # Skip this entry - find the next \bibitem or \end{thebibliography}
                print(f"Removing uncited entry: {key}")
                i += 1
                while i < len(lines):
                    next_line = lines[i]
                    if (re.match(r'\\bibitem\{', next_line) or
                        re.match(r'\\end\{thebibliography\}', next_line)):
                        break
                    i += 1
                continue
            else:
                # Keep this entry
                cleaned_lines.append(line)
        else:
            # Not a bibitem line, keep it
            cleaned_lines.append(line)

        i += 1

    return '\n'.join(cleaned_lines)

def clean_bibliography():
    """Main function to clean the bibliography."""

    cited_keys, uncited_keys, content = extract_cited_keys_and_clean_bibliography()

    if uncited_keys:
        print(f"\nRemoving {len(uncited_keys)} uncited bibliography entries...")

        # Remove uncited entries
        cleaned_content = remove_uncited_bibliography_entries(content, uncited_keys)

        # Write the cleaned content back
        with open("Scoping_Review_Solar_Technology_Maintenance_Challenges.tex", 'w', encoding='utf-8') as f:
            f.write(cleaned_content)

        print(f"✅ Successfully removed {len(uncited_keys)} uncited bibliography entries!")
        print(f"📊 Bibliography now contains only {len(cited_keys)} cited references")
        print("\n🔄 Next steps:")
        print("1. Compile your LaTeX document twice")
        print("2. Check that all citations now display properly")
        print("3. Verify the bibliography is clean and numbered correctly")
    else:
        print("\n✅ No uncited entries found. Bibliography is already clean!")

    return len(cited_keys), len(uncited_keys)

if __name__ == "__main__":
    cited_count, uncited_count = clean_bibliography()
    print(f"\n📈 Final Summary:")
    print(f"   - Cited references: {cited_count}")
    print(f"   - Removed uncited: {uncited_count}")
    print(f"   - Bibliography is now clean and ready!")
