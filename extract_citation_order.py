import re

def extract_citation_order():
    """Extract citations in the exact order they appear in the text."""
    
    with open('Scoping_Review_Solar_Technology_Maintenance_Challenges.tex', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all citations in order
    citation_pattern = r'\\cite[pt]?\{([^}]+)\}'
    citation_matches = re.finditer(citation_pattern, content)
    
    citation_order = []
    seen_citations = set()
    
    for match in citation_matches:
        citation_text = match.group(1)
        # Handle multiple citations like {key1,key2,key3}
        keys = [key.strip() for key in citation_text.split(',')]
        
        for key in keys:
            if key not in seen_citations:
                citation_order.append(key)
                seen_citations.add(key)
    
    print(f"Found {len(citation_order)} unique citations in order:")
    for i, key in enumerate(citation_order, 1):
        print(f"{i:3d}. {key}")
    
    return citation_order

if __name__ == "__main__":
    citation_order = extract_citation_order()
