#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the remaining 51 numbered bibliography entries
"""

import re

def fix_numbered_entries():
    filename = "Scoping_Review_Solar_Technology_Maintenance_Challenges.tex"
    
    # Read the file
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match numbered bibitem entries
    # \bibitem[123]{key} -> \bibitem{key}
    pattern = r'\\bibitem\[(\d+)\]\{([^}]+)\}'
    replacement = r'\\bibitem{\2}'
    
    # Count original numbered entries
    original_count = len(re.findall(pattern, content))
    print(f"Found {original_count} numbered entries to fix")
    
    # Replace all numbered entries with unnumbered ones
    fixed_content = re.sub(pattern, replacement, content)
    
    # Count remaining numbered entries
    remaining_count = len(re.findall(pattern, fixed_content))
    
    # Write the fixed content back
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"Fixed {original_count - remaining_count} numbered entries")
    print(f"Remaining numbered entries: {remaining_count}")
    
    if remaining_count == 0:
        print("\n✅ SUCCESS: All numbered entries fixed!")
        print("Now compile with: pdflatex document.tex (run twice)")
        print("Citations should now display as [1], [2], [3], etc.")
    else:
        print(f"\n⚠️  WARNING: {remaining_count} numbered entries still remain")
    
    return original_count, remaining_count

if __name__ == "__main__":
    original, remaining = fix_numbered_entries()
